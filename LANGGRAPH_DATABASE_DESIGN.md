# LangGraph 数据库设计文档

## 概述

基于 `src/lib/langgraph-state.ts` 中的 LangGraph 状态设计，我们创建了一个完整的数据库架构来支持多智能体交易分析工作流的状态管理和数据持久化。

## 设计原则

1. **状态映射**: 数据库结构直接映射 LangGraph 的状态注解
2. **工作流追踪**: 完整记录工作流的执行过程和状态变化
3. **数据完整性**: 通过外键约束确保数据一致性
4. **性能优化**: 合理的索引设计支持高效查询
5. **扩展性**: 灵活的 JSON 字段支持未来功能扩展

## 数据库架构

### 核心表结构

#### 1. 工作流实例表 (`langgraph_workflows`)

- **用途**: 记录每个 LangGraph 工作流的执行实例
- **关键字段**:
  - `workflow_id`: 工作流唯一标识
  - `task_id`: 关联的任务 ID
  - `thread_id`: LangGraph 线程 ID
  - `ticker`: 分析的股票代码
  - `current_stage`: 当前执行阶段
  - `status`: 工作流状态 (pending/running/completed/failed/cancelled)
  - `progress`: 执行进度百分比

#### 2. 工作流配置表 (`workflow_configs`)

- **用途**: 存储工作流的配置参数
- **映射**: 对应 `TradingAgentAnnotation.config`
- **关键字段**:
  - `deep_think_llm`: 深度思考模型
  - `quick_think_llm`: 快速思考模型
  - `max_debate_rounds`: 最大辩论轮数
  - `research_depth`: 研究深度
  - `online_tools`: 是否启用在线工具

#### 3. 数据收集结果表 (`data_collection_results`)

- **用途**: 存储数据收集阶段的结果
- **映射**: 对应 `TradingAgentAnnotation.data`
- **支持数据类型**: 股价、财务数据、新闻、技术指标、市场数据

#### 4. 分析师结果表 (`analysis_results`)

- **用途**: 存储各个分析师的分析结果
- **映射**: 对应 `TradingAgentAnnotation.analysis`
- **分析师类型**: fundamental, technical, sentiment, news
- **关键字段**:
  - `analysis_result`: 完整分析结果
  - `summary`: 分析摘要
  - `key_findings`: 关键发现
  - `confidence_score`: 置信度评分

#### 5. 研究员结果表 (`researcher_results`)

- **用途**: 存储多头和空头研究员的研究结果
- **映射**: 对应 `TradingAgentAnnotation.research`
- **研究员类型**: bull, bear
- **关键字段**:
  - `research_result`: 研究结果
  - `arguments`: 论证观点
  - `evidence`: 支持证据
  - `target_price`: 目标价格

#### 6. 辩论记录表 (`debate_records`)

- **用途**: 记录多头和空头研究员的辩论过程
- **映射**: 对应 `TradingAgentAnnotation.research.debateRounds`
- **关键字段**:
  - `debate_round`: 辩论轮次
  - `participant_type`: 参与者类型 (bull/bear/moderator)
  - `argument`: 论点内容
  - `supporting_evidence`: 支持证据
  - `strength_score`: 论点强度评分

#### 7. 共识评估结果表 (`consensus_evaluations`)

- **用途**: 存储共识评估的结果
- **关键字段**:
  - `bull_strength`: 多头观点强度
  - `bear_strength`: 空头观点强度
  - `consensus_direction`: 共识方向 (bullish/bearish/neutral)
  - `consensus_confidence`: 共识置信度
  - `synthesis_summary`: 综合总结

#### 8. 风险评估结果表 (`risk_assessments`)

- **用途**: 存储风险评估的详细结果
- **映射**: 对应 `TradingAgentAnnotation.risk`
- **关键字段**:
  - `overall_risk_level`: 整体风险等级
  - `market_risk`: 市场风险评分
  - `company_risk`: 公司风险评分
  - `risk_factors`: 风险因素详情
  - `mitigation_strategies`: 风险缓解策略

#### 9. 交易决策表 (`trading_decisions`)

- **用途**: 存储工作流的最终交易决策
- **映射**: 对应 `TradingAgentAnnotation.decision`
- **关键字段**:
  - `decision_type`: 决策类型 (buy/sell/hold/avoid)
  - `confidence_level`: 决策信心水平
  - `target_price`: 目标价格
  - `stop_loss_price`: 止损价格
  - `decision_rationale`: 决策理由

#### 10. 工作流状态快照表 (`workflow_state_snapshots`)

- **用途**: 存储工作流在各个阶段的完整状态
- **用于**: LangGraph 检查点机制
- **关键字段**:
  - `stage_name`: 阶段名称
  - `state_data`: 完整状态数据 (JSON)
  - `checkpoint_id`: LangGraph 检查点 ID

#### 11. 工作流消息表 (`workflow_messages`)

- **用途**: 存储工作流内部的消息流
- **映射**: 对应 `TradingAgentAnnotation.messages`
- **关键字段**:
  - `message_type`: 消息类型 (human/ai/system/tool)
  - `sender`: 发送者 (节点名称)
  - `content`: 消息内容
  - `stage_name`: 所属阶段

### 视图和存储过程

#### 视图: `workflow_complete_status`

- **用途**: 提供工作流的完整状态概览
- **包含信息**:
  - 各个分析师的完成状态
  - 研究员的完成状态
  - 辩论统计信息
  - 执行时长统计

#### 存储过程

1. `CreateWorkflowInstance`: 创建新的工作流实例
2. `UpdateWorkflowStage`: 更新工作流状态
3. `SaveWorkflowSnapshot`: 保存状态快照

## 技术实现

### TypeScript 类型定义

- **文件**: `src/types/langgraph-database.ts`
- **包含**: 所有表的接口定义和 API 请求/响应类型
- **特点**: 与数据库结构完全对应，确保类型安全

### 数据库操作类

- **文件**: `src/lib/langgraph-database.ts`
- **类名**: `LangGraphDatabase`
- **功能**: 提供所有数据库操作的静态方法
- **特点**:
  - 连接池管理
  - 错误处理
  - 事务支持
  - 类型安全

### API 路由

- **文件**: `src/app/api/langgraph/workflow/route.ts`
- **支持方法**: POST, GET, PUT
- **功能**:
  - 创建工作流实例
  - 查询工作流状态
  - 更新工作流状态

## 工作流状态映射

### LangGraph 状态 → 数据库表

```typescript
TradingAgentAnnotation = {
  ticker → langgraph_workflows.ticker
  date → langgraph_workflows.analysis_date
  config → workflow_configs.*
  data → data_collection_results.*
  analysis → analysis_results.*
  research → researcher_results.* + debate_records.*
  risk → risk_assessments.*
  decision → trading_decisions.*
  currentStage → langgraph_workflows.current_stage
  messages → workflow_messages.*
  status → langgraph_workflows.status
  progress → langgraph_workflows.progress
}
```

## 使用示例

### 1. 创建工作流实例

```typescript
const result = await LangGraphDatabase.createWorkflow({
  workflow_id: 'wf_123',
  task_id: 'task_456',
  ticker: 'AAPL',
  config: {
    deep_think_llm: 'gpt-4o',
    max_debate_rounds: 3,
  },
});
```

### 2. 保存分析师结果

```typescript
await LangGraphDatabase.saveAnalystResult({
  workflow_id: 'wf_123',
  analyst_type: 'fundamental',
  analysis_result: {
    /* 分析数据 */
  },
  summary: '基本面分析摘要',
  confidence_score: 0.85,
});
```

### 3. 记录辩论过程

```typescript
await LangGraphDatabase.saveDebateRecord({
  workflow_id: 'wf_123',
  debate_round: 1,
  participant_type: 'bull',
  argument: '多头观点...',
  supporting_evidence: {
    /* 证据数据 */
  },
  sequence_order: 1,
});
```

### 4. 查询完整状态

```typescript
const status = await LangGraphDatabase.getWorkflowCompleteStatus('wf_123');
console.log(`基本面分析: ${status.fundamental_completed ? '✅' : '❌'}`);
```

## 部署和维护

### 初始化步骤

1. 确保 MySQL 8.0+ 运行
2. 执行 `database/langgraph-schema.sql`
3. 运行 `test-langgraph-database.js` 验证

### 监控和优化

- 定期检查索引使用情况
- 监控查询性能
- 清理过期的状态快照
- 备份重要的工作流数据

### 扩展建议

- 添加工作流性能指标表
- 实现工作流模板机制
- 支持工作流版本管理
- 添加工作流调试信息表

## 总结

这个数据库设计完整地支持了 LangGraph 多智能体交易分析工作流的状态管理需求，提供了：

1. **完整的状态持久化**: 所有工作流状态都能可靠存储
2. **灵活的查询能力**: 支持各种维度的状态查询
3. **高性能**: 合理的索引和视图设计
4. **类型安全**: 完整的 TypeScript 类型定义
5. **易于维护**: 清晰的表结构和存储过程

通过这个设计，LangGraph 工作流可以实现完整的状态管理、错误恢复、性能监控和调试分析。
