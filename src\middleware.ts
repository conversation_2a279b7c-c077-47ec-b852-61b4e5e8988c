/*
 * @Author: ezrealbb <EMAIL>
 * @Date: 2025-07-26 23:20:40
 * @LastEditors: ezrealbb <EMAIL>
 * @LastEditTime: 2025-07-26 23:26:43
 * @FilePath: \trading-agents-frontend\middleware.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { jwtVerify } from 'jose';
import { NextRequest, NextResponse } from 'next/server';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';

// 定义需要保护的路由
const protectedRoutes = ['/create-task', '/tasks', '/analysis', '/messages'];

async function verifyToken(token: string): Promise<boolean> {
  if (!JWT_SECRET) {
    return false;
  }
  try {
    await jwtVerify(token, new TextEncoder().encode(JWT_SECRET));
    return true;
  } catch (error) {
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const accessToken = request.cookies.get('access-token')?.value;
  const isAuthenticated = accessToken ? await verifyToken(accessToken) : false;

  const isProtectedRoute = protectedRoutes.some((route) => pathname.startsWith(route));
  const isAuthRoute = pathname.startsWith('/login') || pathname.startsWith('/register');

  console.log('isProtectedRoute:', isProtectedRoute, pathname, isAuthenticated);

  if (isProtectedRoute && !isAuthenticated) {
    // 如果是受保护的路由但未认证，重定向到登录页
    const url = request.nextUrl.clone();
    url.pathname = '/login';
    url.searchParams.set('redirect', pathname);
    console.log(2222);
    return NextResponse.redirect(url);
  }

  // if (isAuthRoute && isAuthenticated) {
  //   // 如果是登录/注册页但已认证，重定向到任务页
  //   const url = request.nextUrl.clone();
  //   url.pathname = '/tasks'; // 重定向到明确的仪表盘页面
  //   return NextResponse.redirect(url);
  // }
  console.log(1111);

  return NextResponse.next();
}

// 辅助函数：检查管理员权限
function checkAdminPermission(token: string | undefined): boolean {
  if (!token) return false;

  try {
    // 这里实现你的权限检查逻辑
    // 例如：解析 JWT token 或查询数据库
    return true; // 临时返回 true，根据实际需求修改
  } catch {
    return false;
  }
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    // 应用于所有受保护的路由
    '/tasks/:path*',
    '/create-task/:path*',
    '/analysis/:path*',
    '/messages/:path*',

    // 同时应用于认证页面
    '/login',
    '/register',
  ],
};
