# 路由守卫解决方案

## 🎯 问题解决

你提到的问题：**在 Next.js App Router 中，不能在 layout 放置路由守卫，因为 `router.push('/create-task')` 总是会先执行且是异步的**

✅ **已解决**：使用 Next.js Middleware 实现真正的路由守卫，在路由跳转前进行权限检查。

## 📁 核心文件

```
├── middleware.ts                    # 核心路由守卫中间件
├── src/utils/auth.ts               # 简化的认证工具函数
├── test-route-guard.js             # 测试脚本
└── ROUTE_GUARD_README.md           # 本文档
```

## 🔧 核心组件

### 1. Middleware 路由守卫 (`middleware.ts`)

```typescript
// 在路由跳转前执行，真正阻止未认证用户访问
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 受保护的路由
  const protectedRoutes = ['/create-task', '/tasks', '/analysis', '/messages'];

  if (protectedRoutes.some((route) => pathname.startsWith(route))) {
    // 检查认证状态
    const isAuthenticated = !!(
      request.cookies.get('auth-token')?.value ||
      request.cookies.get('session-id')?.value ||
      request.headers.get('authorization')
    );

    if (!isAuthenticated) {
      // 重定向到首页，保存原始路径
      const url = request.nextUrl.clone();
      url.pathname = '/';
      url.searchParams.set('redirect', pathname);
      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next();
}
```

### 2. 认证工具函数 (`src/utils/auth.ts`)

提供完整的认证管理功能：

- `setAuthToken()` - 设置认证 Token
- `getAuthToken()` - 获取认证 Token
- `isAuthenticated()` - 检查认证状态
- `clearAuth()` - 清除认证信息
- `mockLogin()` - 模拟登录（测试用）
- `hasPermission()` - 权限检查

### 3. 首页认证处理 (`src/app/page.tsx`)

- 处理重定向参数
- 显示认证提示弹窗
- 智能的快速开始按钮

## 🚀 使用方法

### 1. 基本使用

```typescript
import { isAuthenticated, mockLogin, logout } from '@/utils/auth';

// 检查认证状态
if (isAuthenticated()) {
  console.log('用户已登录');
}

// 模拟登录（测试用）
mockLogin('user');

// 登出
logout();
```

### 2. 在组件中使用

```typescript
'use client';

import { useEffect, useState } from 'react';
import { isAuthenticated, getUserFromToken } from '@/utils/auth';

export default function MyComponent() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    if (isAuthenticated()) {
      const userInfo = getUserFromToken();
      setUser(userInfo);
    }
  }, []);

  if (!user) {
    return <div>请先登录</div>;
  }

  return <div>欢迎, {user.userId}!</div>;
}
```

## 🧪 测试方法

### 1. 使用测试脚本

在浏览器控制台中运行：

```javascript
// 加载测试脚本
// 然后使用以下命令：

testRouteGuard.clearAuth(); // 清除认证
testRouteGuard.setAuth(); // 设置用户认证
testRouteGuard.setAuth('admin'); // 设置管理员认证
testRouteGuard.checkAuth(); // 检查认证状态
```

### 2. 手动测试步骤

1. **清除认证信息**：

   ```javascript
   document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
   ```

2. **尝试访问受保护路由**：

   - 直接在地址栏输入 `/create-task`
   - 应该被重定向到 `/?redirect=/create-task`

3. **设置认证信息**：

   ```javascript
   document.cookie = 'auth-token=test-token; path=/';
   ```

4. **再次访问**：
   - 现在应该能正常访问 `/create-task`

## 🔒 安全特性

### 1. 多种认证方式支持

- Cookie 认证 (`auth-token`)
- Session 认证 (`session-id`)
- Header 认证 (`Authorization`)

### 2. 角色权限控制

```typescript
// 支持基于角色的权限控制
const roleHierarchy = {
  admin: 3,
  moderator: 2,
  user: 1,
  guest: 0,
};
```

### 3. 路由级权限配置

```typescript
const routePermissions = {
  '/admin': 'admin',
  '/create-task': 'user',
  '/tasks': 'user',
};
```

## 🎨 用户体验

### 1. 智能重定向

- 保存用户原始访问路径
- 登录后自动跳转到目标页面

### 2. 友好的提示

- 权限不足时显示清晰提示
- 认证提示弹窗

### 3. 无缝体验

- 已认证用户无感知
- 未认证用户友好引导

## 🔧 自定义配置

### 1. 修改受保护路由

```typescript
// 在 middleware.ts 中修改
const protectedRoutes = [
  '/create-task',
  '/tasks',
  '/analysis',
  '/messages',
  '/admin', // 添加新的受保护路由
];
```

### 2. 自定义认证逻辑

```typescript
// 在 middleware.ts 中自定义认证检查
const isAuthenticated = customAuthCheck(request);

function customAuthCheck(request: NextRequest): boolean {
  // 你的自定义认证逻辑
  return true;
}
```

### 3. 集成真实认证系统

```typescript
// 替换 mockLogin 为真实登录
async function realLogin(credentials: any) {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials),
  });

  const data = await response.json();
  if (data.success) {
    setAuthToken(data.token);
    handlePostLoginRedirect();
  }
}
```

## 📈 性能优化

1. **中间件执行效率**：只检查必要的路由
2. **Cookie 优化**：设置合适的过期时间
3. **重定向优化**：避免多次重定向

## 🐛 常见问题

### Q: 为什么不在 Layout 中做路由守卫？

A: App Router 的 Layout 是异步加载的，`router.push()` 会立即执行，无法阻止路由跳转。

### Q: 如何调试中间件？

A: 在中间件中添加 `console.log`，查看 Network 面板确认执行。

### Q: 如何处理 API 路由的认证？

A: 在 API 路由中单独处理，或者扩展中间件覆盖 API 路由。

## 🎉 总结

这个解决方案完美解决了 Next.js App Router 中路由守卫的问题：

✅ **真正有效**：使用 Middleware 在路由跳转前拦截  
✅ **用户友好**：智能重定向和认证提示  
✅ **功能完整**：支持多种认证方式和权限控制  
✅ **易于扩展**：模块化设计，便于自定义  
✅ **生产就绪**：包含完整的错误处理和测试工具

现在你可以放心地使用这个路由守卫系统，不再担心 App Router 的异步加载问题！
