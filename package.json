{"name": "trading-agents-frontend", "version": "1.0.0", "description": "TradingAgents 多智能体大语言模型金融交易框架 - 前端界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:test": "node test-db-connection.js", "db:setup": "node setup-database.js"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@langchain/community": "^0.3.6", "@langchain/core": "^0.3.6", "@langchain/langgraph": "^0.3.6", "@langchain/mcp-adapters": "^0.5.3", "@langchain/openai": "^0.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.8.4", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.18.0", "gsap": "^3.13.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "mysql2": "^3.14.1", "next": "^15.3.5", "postcss": "^8.4.31", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "recharts": "^2.8.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "socket.io-client": "^4.7.4", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "uuid": "^11.1.0", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "eslint": "^8.0.0", "eslint-config-next": "^15.3.5", "jest": "^29.7.0", "jest-environment-node": "^29.7.0"}, "engines": {"node": ">=18.0.0"}}