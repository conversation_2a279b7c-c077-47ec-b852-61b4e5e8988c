# 数据库结构简化说明

## 📋 文件合并

已将 `database/init_langgraph_schema.sql` 合并到 `database/langgraph-schema.sql` 中，因为两个文件内容几乎完全相同，没有必要维护两个重复的文件。

## 🗂️ 当前数据库文件结构

```
database/
├── schema.sql                    # 原有数据库架构（tasks, messages, analysis_results 等）
├── langgraph-schema.sql          # LangGraph 工作流架构（完整初始化脚本）
├── migration_add_decision_type.sql  # 枚举类型修复迁移脚本
└── init_database.sql             # 主数据库初始化脚本
```

## 🎯 各文件用途

### `schema.sql`

- **用途**: 原有的交易分析系统数据库架构
- **包含**: tasks, messages, tool_calls, analysis_results, analysis_steps, system_logs 表
- **特点**: 基础的任务和消息管理

### `langgraph-schema.sql` ⭐

- **用途**: LangGraph 工作流状态管理的完整数据库架构
- **包含**:
  - 11 个专用表（工作流、分析师、研究员、辩论、共识、风险、决策等）
  - 1 个状态视图（workflow_complete_status）
  - 3 个存储过程（CreateWorkflowInstance, UpdateWorkflowStage, SaveWorkflowSnapshot）
- **特点**: 完整的多智能体工作流状态管理

### `migration_add_decision_type.sql`

- **用途**: 修复 analysis_results 表的 result_type 枚举
- **包含**: 添加 'decision' 枚举值的 ALTER TABLE 语句
- **特点**: 解决代码与数据库不匹配的问题

### `init_database.sql`

- **用途**: 主数据库初始化入口
- **包含**: 调用其他 SQL 文件的 SOURCE 命令
- **特点**: 统一的初始化入口点

## 🚀 使用方式

### 全新安装

```bash
# 方法 1: 使用主初始化脚本
mysql -u root -p < database/init_database.sql

# 方法 2: 分步执行
mysql -u root -p < database/schema.sql
mysql -u root -p < database/langgraph-schema.sql
```

### 现有系统升级

```bash
# 如果已有基础表，只需添加 LangGraph 表
mysql -u root -p trading_analysis < database/langgraph-schema.sql

# 如果需要修复枚举类型
mysql -u root -p trading_analysis < database/migration_add_decision_type.sql
```

### 验证安装

```bash
# 验证基础架构
node test-database-fix.js

# 验证 LangGraph 架构
node test-langgraph-schema-only.js

# 完整功能测试
node test-analysis-workflow.js
```

## ✅ 合并的好处

1. **减少维护负担**: 不需要同步两个几乎相同的文件
2. **避免混淆**: 开发者不会疑惑应该使用哪个文件
3. **简化部署**: 只需要一个文件就能完成 LangGraph 数据库初始化
4. **减少错误**: 避免两个文件不同步导致的问题

## 🔄 迁移影响

### 已更新的文件

- ✅ `test-langgraph-schema-only.js` - 移除对已删除文件的引用
- ✅ `LANGGRAPH_DATABASE_DESIGN.md` - 更新文档引用
- ✅ `ANALYSIS_WRITE_IMPLEMENTATION.md` - 更新文档引用
- ✅ `database/langgraph-schema.sql` - 更新注释说明

### 不受影响的功能

- ✅ 所有数据库操作类正常工作
- ✅ API 路由正常工作
- ✅ React Hooks 正常工作
- ✅ 测试脚本正常工作

## 📝 总结

这个合并是一个纯粹的文件整理优化，没有改变任何功能。`database/langgraph-schema.sql` 现在是 LangGraph 数据库架构的唯一权威来源，包含了完整的初始化功能。
