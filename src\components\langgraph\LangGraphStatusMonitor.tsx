'use client';

import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { useLangGraphAgent } from '@/hooks/useLangGraphAgent';
import { useCallback, useEffect, useState } from 'react';

interface LangGraphStatusMonitorProps {
  className?: string;
}

export function LangGraphStatusMonitor({ className }: LangGraphStatusMonitorProps) {
  const {
    threadId,
    connectionStatus,
    sessionInfo,
    isProcessing,
    isStreaming,
    currentStep,
    progress,
    error,
    messages,
  } = useLangGraphAgent();

  const [activeSessions, setActiveSessions] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // 刷新活跃会话列表
  const refreshSessions = useCallback(async () => {
    setRefreshing(true);
    try {
      // 这里应该调用后端 API 获取活跃会话列表
      // 暂时模拟数据
      const mockSessions = [
        {
          threadId: threadId || 'demo-session',
          createdAt: new Date(),
          messageCount: messages.length,
          isActive: isProcessing || isStreaming,
          lastActivity: new Date(),
        },
      ];
      setActiveSessions(mockSessions);
    } catch (error) {
      console.error('刷新会话列表失败:', error);
    } finally {
      setRefreshing(false);
    }
  }, [threadId, messages.length, isProcessing, isStreaming]);

  useEffect(() => {
    refreshSessions();
    const interval = setInterval(refreshSessions, 10000); // 每10秒刷新一次
    return () => clearInterval(interval);
  }, [threadId, messages.length, isProcessing, isStreaming, refreshSessions]);

  const getConnectionStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-500';
      case 'connecting':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getConnectionStatusText = (status: string) => {
    switch (status) {
      case 'connected':
        return '已连接';
      case 'connecting':
        return '连接中';
      case 'error':
        return '连接错误';
      default:
        return '未连接';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>LangGraph 状态监控</span>
          <Button size="sm" variant="ghost" onClick={refreshSessions} disabled={refreshing}>
            {refreshing ? '刷新中...' : '刷新'}
          </Button>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 连接状态 */}
        <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${getConnectionStatusColor(connectionStatus)}`} />
            <span className="font-medium">连接状态</span>
          </div>
          <Badge variant={connectionStatus === 'connected' ? 'success' : 'warning'}>
            {getConnectionStatusText(connectionStatus)}
          </Badge>
        </div>

        {/* 当前会话信息 */}
        {threadId && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">当前会话</h4>
            <div className="p-3 border rounded-lg space-y-2">
              <div className="flex justify-between text-sm">
                <span>会话 ID:</span>
                <code className="text-xs bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded">
                  {threadId.slice(-12)}
                </code>
              </div>

              <div className="flex justify-between text-sm">
                <span>消息数量:</span>
                <span>{sessionInfo.messageCount}</span>
              </div>

              {sessionInfo.createdAt && (
                <div className="flex justify-between text-sm">
                  <span>创建时间:</span>
                  <span>{sessionInfo.createdAt.toLocaleTimeString()}</span>
                </div>
              )}

              {sessionInfo.updatedAt && (
                <div className="flex justify-between text-sm">
                  <span>最后更新:</span>
                  <span>{sessionInfo.updatedAt.toLocaleTimeString()}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 处理状态 */}
        {(isProcessing || isStreaming) && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">处理状态</h4>
            <div className="p-3 border rounded-lg space-y-2">
              <div className="flex items-center gap-2">
                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />
                <span className="text-sm">{isStreaming ? '流式处理中' : '处理中'}</span>
              </div>

              {currentStep && (
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  当前步骤: {currentStep}
                </div>
              )}

              {progress > 0 && (
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>进度</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm text-red-600">错误信息</h4>
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        )}

        {/* 活跃会话列表 */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">活跃会话</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {activeSessions.length > 0 ? (
              activeSessions.map((session, index) => (
                <div key={index} className="p-2 border rounded text-sm">
                  <div className="flex justify-between items-center">
                    <code className="text-xs">{session.threadId.slice(-12)}</code>
                    <div className="flex items-center gap-2">
                      {session.isActive && (
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      )}
                      <span className="text-xs text-slate-500">{session.messageCount} 消息</span>
                    </div>
                  </div>
                  <div className="text-xs text-slate-500 mt-1">
                    最后活动: {session.lastActivity.toLocaleTimeString()}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-slate-500 text-center py-4">暂无活跃会话</div>
            )}
          </div>
        </div>

        {/* 系统信息 */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">系统信息</h4>
          <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg text-xs space-y-1">
            <div>前端版本: 1.0.0</div>
            <div>LangGraph 客户端: 已加载</div>
            <div>WebSocket 支持: {typeof WebSocket !== 'undefined' ? '是' : '否'}</div>
            <div>最后检查: {new Date().toLocaleTimeString()}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
