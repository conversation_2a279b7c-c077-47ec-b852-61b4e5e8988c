'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  ChartBarIcon,
  NewspaperIcon,
  HeartIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { AgentStatus } from '@/lib/api';

interface AgentStatusPanelProps {
  agentStatuses: AgentStatus[];
  selectedAnalysts: string[];
}

export function AgentStatusPanel({ agentStatuses, selectedAnalysts }: AgentStatusPanelProps) {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const agentConfig = {
    market: {
      name: '市场分析师',
      description: '分析技术指标和价格走势',
      icon: ChartBarIcon,
      color: 'blue',
      tasks: ['获取历史价格数据', '计算技术指标', '分析价格趋势', '生成技术分析报告'],
    },
    social: {
      name: '社交媒体分析师',
      description: '分析社交媒体情绪和舆论',
      icon: HeartIcon,
      color: 'pink',
      tasks: ['收集社交媒体数据', '情绪分析', '舆论趋势分析', '生成情绪报告'],
    },
    news: {
      name: '新闻分析师',
      description: '分析新闻事件和宏观经济',
      icon: NewspaperIcon,
      color: 'green',
      tasks: ['收集新闻数据', '事件影响分析', '宏观经济分析', '生成新闻分析报告'],
    },
    fundamentals: {
      name: '基本面分析师',
      description: '分析财务报表和公司基本面',
      icon: UserIcon,
      color: 'purple',
      tasks: ['获取财务数据', '财务比率分析', '估值分析', '生成基本面报告'],
    },
    bull_researcher: {
      name: '多头研究员',
      description: '寻找买入机会和积极因素',
      icon: UserIcon,
      color: 'green',
      tasks: ['分析积极因素', '构建多头论点', '风险评估', '生成买入建议'],
    },
    bear_researcher: {
      name: '空头研究员',
      description: '识别风险因素和消极因素',
      icon: UserIcon,
      color: 'red',
      tasks: ['分析风险因素', '构建空头论点', '下行风险评估', '生成卖出建议'],
    },
    debate_moderator: {
      name: '辩论主持人',
      description: '主持多空辩论并评估共识',
      icon: UserIcon,
      color: 'indigo',
      tasks: ['组织辩论', '评估论点', '寻求共识', '生成辩论总结'],
    },
    risk_manager: {
      name: '风险管理师',
      description: '评估投资风险和制定风控策略',
      icon: UserIcon,
      color: 'orange',
      tasks: ['风险识别', '风险量化', '风控策略', '生成风险报告'],
    },
    portfolio_manager: {
      name: '投资组合经理',
      description: '制定最终投资决策',
      icon: UserIcon,
      color: 'purple',
      tasks: ['综合分析', '决策制定', '仓位管理', '生成投资建议'],
    },
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'running':
        return (
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-slate-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20';
      case 'running':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20';
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20';
      default:
        return 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'running':
        return '运行中';
      case 'error':
        return '错误';
      default:
        return '等待中';
    }
  };

  // 创建代理状态映射
  const agentStatusMap = agentStatuses.reduce((acc, agent) => {
    acc[agent.id] = agent;
    return acc;
  }, {} as Record<string, AgentStatus>);

  return (
    <div className="space-y-6">
      {/* 总体状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>代理团队总览</span>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>已完成: {agentStatuses.filter((a) => a.status === 'completed').length}</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span>运行中: {agentStatuses.filter((a) => a.status === 'running').length}</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                <span>等待中: {agentStatuses.filter((a) => a.status === 'idle').length}</span>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(
                  (agentStatuses.filter((a) => a.status === 'completed').length /
                    Math.max(agentStatuses.length, 1)) *
                    100
                )}
                %
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">完成率</div>
            </div>
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(
                  agentStatuses.reduce((sum, agent) => sum + agent.progress, 0) /
                    Math.max(agentStatuses.length, 1)
                )}
                %
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">平均进度</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{agentStatuses.length}</div>
              <div className="text-sm text-slate-600 dark:text-slate-400">总代理数</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {agentStatuses.filter((a) => a.status === 'error').length}
              </div>
              <div className="text-sm text-slate-600 dark:text-slate-400">错误数</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 分析师状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>分析师团队状态</span>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              {showDetails ? '隐藏详情' : '显示详情'}
            </button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {selectedAnalysts.map((analystId, index) => {
              const config = agentConfig[analystId as keyof typeof agentConfig];
              const status = agentStatusMap[analystId] || {
                id: analystId,
                name: config?.name || analystId,
                status: 'idle',
                progress: 0,
                lastUpdate: new Date().toISOString(),
              };

              if (!config) return null;

              return (
                <motion.div
                  key={analystId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md ${getStatusColor(
                    status.status
                  )} ${selectedAgent === analystId ? 'ring-2 ring-blue-500' : ''}`}
                  onClick={() => setSelectedAgent(selectedAgent === analystId ? null : analystId)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`p-2 rounded-lg bg-${config.color}-100 dark:bg-${config.color}-900/20`}
                      >
                        <config.icon className={`h-5 w-5 text-${config.color}-600`} />
                      </div>
                      <div>
                        <h4 className="font-medium text-slate-900 dark:text-white">
                          {config.name}
                        </h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(status.status)}
                      <span className="text-xs text-slate-500">
                        {selectedAgent === analystId ? '▼' : '▶'}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                        状态: {getStatusText(status.status)}
                      </span>
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                        {status.progress}%
                      </span>
                    </div>

                    <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full bg-${config.color}-500`}
                        initial={{ width: 0 }}
                        animate={{ width: `${status.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>

                    {status.message && (
                      <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                        💬 {status.message}
                      </p>
                    )}

                    <p className="text-xs text-slate-500 dark:text-slate-500">
                      🕒 最后更新: {new Date(status.lastUpdate).toLocaleTimeString()}
                    </p>

                    {/* 展开的详细信息 */}
                    {selectedAgent === analystId && showDetails && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-600"
                      >
                        <h5 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                          任务清单:
                        </h5>
                        <div className="space-y-1">
                          {config.tasks.map((task, taskIndex) => (
                            <div key={taskIndex} className="flex items-center space-x-2 text-xs">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  taskIndex <
                                  Math.floor((status.progress / 100) * config.tasks.length)
                                    ? 'bg-green-500'
                                    : taskIndex ===
                                      Math.floor((status.progress / 100) * config.tasks.length)
                                    ? 'bg-blue-500 animate-pulse'
                                    : 'bg-slate-300'
                                }`}
                              ></div>
                              <span
                                className={`${
                                  taskIndex <
                                  Math.floor((status.progress / 100) * config.tasks.length)
                                    ? 'text-green-600 line-through'
                                    : taskIndex ===
                                      Math.floor((status.progress / 100) * config.tasks.length)
                                    ? 'text-blue-600 font-medium'
                                    : 'text-slate-500'
                                }`}
                              >
                                {task}
                              </span>
                            </div>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 其他代理状态 */}
      <Card>
        <CardHeader>
          <CardTitle>其他代理状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* 研究团队 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {['bull_researcher', 'bear_researcher'].map((agentId) => {
                const status = agentStatusMap[agentId];
                const isBull = agentId === 'bull_researcher';
                const config = agentConfig[agentId as keyof typeof agentConfig];

                return (
                  <div
                    key={agentId}
                    className={`p-4 border rounded-lg ${
                      status
                        ? getStatusColor(status.status)
                        : 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`p-2 rounded-lg ${
                            isBull
                              ? 'bg-green-100 dark:bg-green-900/20'
                              : 'bg-red-100 dark:bg-red-900/20'
                          }`}
                        >
                          <span className={`text-lg ${isBull ? 'text-green-600' : 'text-red-600'}`}>
                            {isBull ? '🐂' : '🐻'}
                          </span>
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {config?.name || (isBull ? '多头研究员' : '空头研究员')}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {config?.description || (isBull ? '寻找买入机会' : '识别风险因素')}
                          </p>
                        </div>
                      </div>
                      {status && getStatusIcon(status.status)}
                    </div>

                    {status && (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            状态: {getStatusText(status.status)}
                          </span>
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {status.progress}%
                          </span>
                        </div>

                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full ${isBull ? 'bg-green-500' : 'bg-red-500'}`}
                            initial={{ width: 0 }}
                            animate={{ width: `${status.progress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>

                        {status.message && (
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                            💬 {status.message}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* 其他关键代理 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                {
                  id: 'debate_moderator',
                  name: '辩论主持人',
                  icon: '⚖️',
                  description: '主持多空辩论',
                },
                { id: 'risk_manager', name: '风险管理师', icon: '🛡️', description: '评估投资风险' },
                {
                  id: 'portfolio_manager',
                  name: '投资组合经理',
                  icon: '💼',
                  description: '制定最终决策',
                },
              ].map((agent) => {
                const status = agentStatusMap[agent.id];
                const config = agentConfig[agent.id as keyof typeof agentConfig];

                return (
                  <div
                    key={agent.id}
                    className={`p-4 border rounded-lg ${
                      status
                        ? getStatusColor(status.status)
                        : 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 rounded-lg bg-slate-100 dark:bg-slate-800">
                          <span className="text-lg">{agent.icon}</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">
                            {config?.name || agent.name}
                          </h4>
                          <p className="text-sm text-slate-600 dark:text-slate-400">
                            {config?.description || agent.description}
                          </p>
                        </div>
                      </div>
                      {status && getStatusIcon(status.status)}
                    </div>

                    {status && (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            状态: {getStatusText(status.status)}
                          </span>
                          <span className="text-sm text-slate-600 dark:text-slate-400">
                            {status.progress}%
                          </span>
                        </div>

                        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full bg-${config?.color || 'slate'}-500`}
                            initial={{ width: 0 }}
                            animate={{ width: `${status.progress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>

                        {status.message && (
                          <p className="text-xs text-slate-600 dark:text-slate-400 mt-2">
                            💬 {status.message}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
