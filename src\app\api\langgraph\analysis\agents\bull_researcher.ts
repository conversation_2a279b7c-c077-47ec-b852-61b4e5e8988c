import { TradingAgentAnnotation } from '@/lib/langgraph-state';
import { AIMessage } from '@langchain/core/messages';
import { PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';

const bullResearcherPrompt = PromptTemplate.fromTemplate(`
你是一位资深的多头研究员，专门从看涨角度深度挖掘投资机会。你拥有15年的A股投资研究经验，擅长发现被市场低估的价值和成长机会。你的任务是基于分析师团队的初步分析，从多头角度进行深度研究和论证。

【研究任务】
股票代码: {ticker}
分析日期: {date}
研究深度: {researchDepth}

【分析师团队报告】
{analysisReports}

【多头研究框架】
请从以下角度进行深度多头研究：

1. **价值发现论证**
   - 挖掘被市场忽视的价值点
   - 分析估值修复的催化剂
   - 识别潜在的价值重估机会
   - 对比同行业估值水平

2. **成长逻辑构建**
   - 分析业务增长的核心驱动力
   - 评估市场空间和渗透率提升
   - 识别新业务和新产品的增长潜力
   - 构建中长期成长路径

3. **竞争优势强化**
   - 深度分析公司的护城河
   - 评估技术壁垒和品牌价值
   - 分析规模效应和网络效应
   - 识别竞争优势的可持续性

4. **催化剂识别**
   - 政策利好和行业趋势
   - 公司治理改善和管理层变化
   - 新产品发布和市场拓展
   - 并购重组和资产注入机会

5. **技术面支撑**
   - 从技术角度论证上涨空间
   - 识别关键突破位和目标价位
   - 分析资金流入和主力行为
   - 评估技术形态的看涨信号

6. **风险对冲策略**
   - 识别主要风险点并提出应对策略
   - 分析最坏情况下的下行保护
   - 提出风险控制和仓位管理建议

7. **投资建议论证**
   - 明确的买入理由和逻辑链条
   - 目标价位和时间周期设定
   - 分阶段建仓和加仓策略
   - 关键监控指标和退出条件

【输出要求】
- 论证要有理有据，逻辑清晰
- 重点突出最具说服力的多头观点
- 提供具体的数据支撑和案例对比
- 承认风险但重点论证机会大于风险
- 为后续辩论准备充分的论据
`);

export async function bullResearcherNode(state: typeof TradingAgentAnnotation.State) {
  const { ticker, date, config, analysis, messages } = state;

  console.log(`[多头研究员] 开始深度研究股票: ${ticker}`);

  try {
    // 检查分析师报告是否可用
    if (!analysis || Object.keys(analysis).length === 0) {
      throw new Error('分析师团队报告不可用，无法进行深度研究');
    }

    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY;
    const OPENAI_BASE_URL =
      process.env.OPENAI_BASE_URL ||
      process.env.NEXT_PUBLIC_OPENAI_BASE_URL ||
      'https://api.openai.com/v1';

    const llm = new ChatOpenAI({
      modelName: config.deepThinkLLM || 'gpt-4o',
      temperature: 0.2, // 稍高的温度以获得更有创意的论证
      apiKey: OPENAI_API_KEY,
      configuration: {
        baseURL: OPENAI_BASE_URL,
      },
    });

    // 整理分析师报告
    const analysisReports = formatAnalysisReports(analysis);

    const prompt = await bullResearcherPrompt.format({
      ticker,
      date,
      researchDepth: config.researchDepth || 'standard',
      analysisReports,
    });

    console.log(`[多头研究员] 正在进行深度多头研究...`);
    const response = await llm.invoke(prompt);
    const researchReport = response.content as string;

    // 提取关键论点和支撑数据
    const keyArguments = extractBullArguments(researchReport);
    const catalysts = extractCatalysts(researchReport);
    const targetPrice = extractTargetPrice(researchReport);
    const confidence = calculateBullConfidence(analysis, keyArguments);

    // 生成结构化摘要
    const summary = generateBullSummary(researchReport, keyArguments, targetPrice);

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【多头研究员报告】\n\n${researchReport}`,
        name: 'BullResearcher',
      }),
    ];

    // 更新研究状态
    const research = {
      ...state.research,
      bull: {
        summary,
        report: researchReport,
        keyArguments,
        catalysts,
        targetPrice,
        confidence,
        analyst: 'BullResearcher',
        timestamp: new Date().toISOString(),
      },
    };

    console.log(`[多头研究员] 研究完成，置信度: ${confidence.toFixed(2)}`);
    return {
      messages: newMessages,
      research,
      currentStage: 'bull_research_completed',
      progress: Math.min(state.progress + 15, 100),
    };
  } catch (error) {
    console.error('[多头研究员] 研究失败:', error);
    const errorMessage = `多头研究失败: ${error instanceof Error ? error.message : String(error)}`;

    const newMessages = [
      ...messages,
      new AIMessage({
        content: `【多头研究员】${errorMessage}`,
        name: 'BullResearcher',
      }),
    ];

    const research = {
      ...state.research,
      bull: {
        summary: '多头研究失败',
        report: errorMessage,
        error: true,
        analyst: 'BullResearcher',
        timestamp: new Date().toISOString(),
      },
    };

    return { messages: newMessages, research };
  }
}

// 辅助函数：格式化分析师报告
function formatAnalysisReports(analysis: any): string {
  let reports = '';

  if (analysis.fundamental) {
    reports += `\n【基本面分析】\n${analysis.fundamental.summary}\n`;
    if (analysis.fundamental.keyMetrics) {
      reports += `关键指标: ${JSON.stringify(analysis.fundamental.keyMetrics, null, 2)}\n`;
    }
  }

  if (analysis.technical) {
    reports += `\n【技术分析】\n${analysis.technical.summary}\n`;
    if (analysis.technical.technicalSignals) {
      reports += `技术信号: ${JSON.stringify(analysis.technical.technicalSignals, null, 2)}\n`;
    }
  }

  if (analysis.sentiment) {
    reports += `\n【情绪分析】\n${analysis.sentiment.summary}\n`;
  }

  if (analysis.news) {
    reports += `\n【新闻分析】\n${analysis.news.summary}\n`;
  }

  return reports || '分析师报告暂不可用';
}

// 辅助函数：提取多头论点
function extractBullArguments(report: string): string[] {
  const keyArguments: string[] = [];

  // 使用正则表达式提取关键论点
  const patterns = [
    /价值[重估|修复|发现]/g,
    /成长[潜力|空间|驱动]/g,
    /竞争优势|护城河|壁垒/g,
    /催化剂|利好|机会/g,
    /突破|上涨|看涨/g,
  ];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of patterns) {
      if (pattern.test(line) && line.length > 10 && line.length < 200) {
        keyArguments.push(line.trim());
        break;
      }
    }
  }

  return keyArguments.slice(0, 5); // 返回前5个关键论点
}

// 辅助函数：提取催化剂
function extractCatalysts(report: string): string[] {
  const catalysts: string[] = [];

  const catalystPatterns = [
    /政策[利好|支持]/g,
    /新产品|新业务/g,
    /并购|重组|注入/g,
    /业绩[改善|增长]/g,
    /市场[拓展|份额]/g,
  ];

  const lines = report.split('\n');
  for (const line of lines) {
    for (const pattern of catalystPatterns) {
      if (pattern.test(line) && line.length > 10 && line.length < 150) {
        catalysts.push(line.trim());
        break;
      }
    }
  }

  return catalysts.slice(0, 3); // 返回前3个催化剂
}

// 辅助函数：提取目标价位
function extractTargetPrice(report: string): number | null {
  const pricePatterns = [
    /目标价[位]?[：:]\s*([0-9.]+)/,
    /目标[：:]\s*([0-9.]+)/,
    /上涨[至到]\s*([0-9.]+)/,
  ];

  for (const pattern of pricePatterns) {
    const match = report.match(pattern);
    if (match) {
      return parseFloat(match[1]);
    }
  }

  return null;
}

// 辅助函数：计算多头置信度
function calculateBullConfidence(analysis: any, keyArguments: string[]): number {
  let confidence = 0.5; // 基础置信度

  // 基于分析师报告的积极信号
  if (
    analysis.fundamental?.investmentRating === '推荐' ||
    analysis.fundamental?.investmentRating === '强烈推荐'
  ) {
    confidence += 0.15;
  }

  if (analysis.technical?.tradingSignal === '买入') {
    confidence += 0.15;
  }

  if (analysis.sentiment?.overallSentiment === 'positive') {
    confidence += 0.1;
  }

  if (analysis.news?.sentiment > 0.6) {
    confidence += 0.1;
  }

  // 基于论点数量和质量
  confidence += Math.min(keyArguments.length * 0.02, 0.1);

  return Math.min(confidence, 0.95); // 最高95%置信度
}

// 辅助函数：生成多头摘要
function generateBullSummary(
  report: string,
  keyArguments: string[],
  targetPrice: number | null
): string {
  const lines = report.split('\n').filter((line) => line.trim());
  const firstParagraph = lines.slice(0, 2).join(' ');

  let summary = `多头观点 - ${firstParagraph.substring(0, 120)}...`;

  if (keyArguments.length > 0) {
    summary += ` 核心论点: ${keyArguments[0].substring(0, 50)}...`;
  }

  if (targetPrice) {
    summary += ` 目标价: ${targetPrice}`;
  }

  return summary;
}
