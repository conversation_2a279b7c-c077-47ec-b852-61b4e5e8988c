// Enhanced LangGraph Database Operations - Extended for Analysis Data Storage
// Extends the existing LangGraphDatabase with complete workflow status queries,
// batch operations, and analysis statistics functionality

import mysql from 'mysql2/promise';
import {
  AnalystReport,
  AnalystType,
  CreateWorkflowRequest,
  FinalDecision,
  ResearchReport,
  SaveAnalystReportRequest,
  Workflow,
  WorkflowEvent,
  WorkflowQueryOptions,
} from '../types/langgraph-database';
import LangGraphDatabase from './langgraph-database';

// ============================================================================
// Extended Types for Enhanced Functionality
// ============================================================================

export interface CompleteWorkflowStatus {
  workflow: Workflow;
  analystReports: AnalystReport[];
  researchReports: ResearchReport[];
  finalDecision: FinalDecision | null;
  recentEvents: WorkflowEvent[];
}

export interface CreateCompleteWorkflowRequest extends CreateWorkflowRequest {
  config?: any;
}

export interface AgentStateUpdate {
  workflow_id: string;
  analyst_type: string;
  status: 'completed' | 'failed';
  execution_time_ms?: number;
}

export interface StatisticsOptions {
  date_from?: string;
  date_to?: string;
  ticker?: string;
}

export interface AnalysisStatistics {
  basic: {
    total_workflows: number;
    completed_workflows: number;
    failed_workflows: number;
    avg_duration_seconds: number;
  };
  successRates: Array<{
    ticker: string;
    total_count: number;
    success_count: number;
  }>;
}

export interface WorkflowStatusSummary {
  workflow_id: string;
  status: string;
  progress: number;
  current_stage: string;
  analyst_completion: {
    fundamental: boolean;
    technical: boolean;
    sentiment: boolean;
    news: boolean;
  };
  research_completion: {
    bull: boolean;
    bear: boolean;
  };
  final_decision_completed: boolean;
}

// ============================================================================
// Enhanced LangGraph Database Class
// ============================================================================

export class EnhancedLangGraphDatabase extends LangGraphDatabase {
  // ============================================================================
  // Enhanced Workflow Management
  // ============================================================================
  constructor() {}

  /**
   * Creates a complete workflow with all necessary initialization
   * Requirements: 需求 1.1, 需求 7.1
   */
  static async createCompleteWorkflow(request: CreateCompleteWorkflowRequest): Promise<string> {
    return this.withEnhancedConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        // 1. Create the main workflow
        const workflowId = await this.createWorkflow(request);

        // 2. Initialize analyst report placeholders
        await this.initializeAnalystStates(connection, workflowId, request.config);

        // 3. Log initial event
        await this.logWorkflowEvent({
          workflow_id: workflowId,
          stage_name: 'initialization',
          event_type: 'log',
          content: '工作流初始化完成',
          metadata: { config: request.config },
        });

        await connection.commit();
        return workflowId;
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  /**
   * Initialize analyst states for a workflow
   * Requirements: 需求 1.1, 需求 1.2
   */
  private static async initializeAnalystStates(
    connection: mysql.PoolConnection,
    workflowId: string,
    config?: any
  ): Promise<void> {
    const analystTypes: AnalystType[] = ['fundamental', 'technical', 'sentiment', 'news'];

    for (const analystType of analystTypes) {
      const reportId = this.generateId('rep');
      await connection.execute(
        `INSERT INTO analyst_reports (report_id, workflow_id, analyst_type, summary, status, execution_time_ms)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [reportId, workflowId, analystType, null, 'pending', null]
      );
    }
  }

  /**
   * Get complete workflow status with all related data
   * Requirements: 需求 1.3, 需求 2.1, 需求 2.2
   */
  static async getCompleteWorkflowStatus(workflow_id: string): Promise<CompleteWorkflowStatus> {
    return this.withEnhancedConnection(async (connection) => {
      // Get workflow basic information
      const workflow = await this.getWorkflow(workflow_id);
      if (!workflow) {
        throw new Error(`工作流不存在: ${workflow_id}`);
      }

      // Get all analyst reports with details
      const [analystReports] = await connection.execute(
        `SELECT ar.*, 
                tad.trading_signal, tad.trend_signal, tad.support_level, tad.resistance_level,
                tad.stop_loss_level, tad.target_price, tad.rsi_value, tad.macd_signal, tad.key_levels,
                sad.overall_sentiment, sad.sentiment_score, sad.positive_news_count, 
                sad.negative_news_count, sad.neutral_news_count, sad.key_drivers
         FROM analyst_reports ar
         LEFT JOIN technical_analysis_details tad ON ar.report_id = tad.report_id
         LEFT JOIN sentiment_analysis_details sad ON ar.report_id = sad.report_id
         WHERE ar.workflow_id = ? 
         ORDER BY ar.created_at`,
        [workflow_id]
      );

      // Get all research reports with arguments
      const [researchReports] = await connection.execute(
        `SELECT rr.*, 
                GROUP_CONCAT(
                  CONCAT(ra.argument_type, ':', ra.content, '|', COALESCE(ra.strength_score, 0))
                  ORDER BY ra.sequence_order SEPARATOR ';;'
                ) as arguments_data
         FROM research_reports rr
         LEFT JOIN research_arguments ra ON rr.report_id = ra.report_id
         WHERE rr.workflow_id = ?
         GROUP BY rr.report_id
         ORDER BY rr.created_at`,
        [workflow_id]
      );

      // Get final decision
      const [decisions] = await connection.execute(
        'SELECT * FROM final_decisions WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 1',
        [workflow_id]
      );

      // Get recent events
      const [events] = await connection.execute(
        'SELECT * FROM workflow_events WHERE workflow_id = ? ORDER BY created_at DESC LIMIT 50',
        [workflow_id]
      );

      const decisionsArray = decisions as FinalDecision[];
      const eventsArray = events as WorkflowEvent[];

      return {
        workflow,
        analystReports: analystReports as AnalystReport[],
        researchReports: researchReports as ResearchReport[],
        finalDecision: decisionsArray.length > 0 ? decisionsArray[0] : null,
        recentEvents: eventsArray,
      };
    });
  }

  // ============================================================================
  // Batch Operations
  // ============================================================================

  /**
   * Batch update agent states
   * Requirements: 需求 1.2, 需求 7.2
   */
  static async batchUpdateAgentStates(updates: AgentStateUpdate[]): Promise<void> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        for (const update of updates) {
          await connection.execute(
            `UPDATE analyst_reports 
             SET status = ?, execution_time_ms = ?, updated_at = NOW()
             WHERE workflow_id = ? AND analyst_type = ?`,
            [update.status, update.execution_time_ms, update.workflow_id, update.analyst_type]
          );
        }
        await connection.commit();
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  /**
   * Batch save analyst results with details
   * Requirements: 需求 1.2, 需求 1.3
   */
  static async batchSaveAnalystResults(results: SaveAnalystReportRequest[]): Promise<string[]> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const reportIds: string[] = [];

        for (const result of results) {
          const reportId = await this.saveAnalystReport(result);
          reportIds.push(reportId);
        }

        await connection.commit();
        return reportIds;
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  // ============================================================================
  // Analysis Statistics
  // ============================================================================

  /**
   * Get comprehensive analysis statistics
   * Requirements: 需求 4.6, 需求 6.1, 需求 6.2
   */
  static async getAnalysisStatistics(options: StatisticsOptions = {}): Promise<AnalysisStatistics> {
    return this.withConnection(async (connection) => {
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (options.date_from) {
        whereClause += ' AND created_at >= ?';
        params.push(options.date_from);
      }

      if (options.date_to) {
        whereClause += ' AND created_at <= ?';
        params.push(options.date_to);
      }

      if (options.ticker) {
        whereClause += ' AND ticker = ?';
        params.push(options.ticker);
      }

      // Get basic statistics
      const [basicStats] = await connection.execute(
        `SELECT 
          COUNT(*) as total_workflows,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_workflows,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_workflows,
          AVG(CASE WHEN status = 'completed' AND started_at IS NOT NULL AND completed_at IS NOT NULL 
              THEN TIMESTAMPDIFF(SECOND, started_at, completed_at) END) as avg_duration_seconds
         FROM workflows ${whereClause}`,
        params
      );

      // Get success rates by ticker
      const [successRates] = await connection.execute(
        `SELECT 
          ticker,
          COUNT(*) as total_count,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as success_count
         FROM workflows ${whereClause}
         GROUP BY ticker
         ORDER BY total_count DESC
         LIMIT 10`,
        params
      );

      const basicStatsArray = basicStats as any[];
      const successRatesArray = successRates as any[];

      return {
        basic: basicStatsArray[0],
        successRates: successRatesArray,
      };
    });
  }

  /**
   * Get workflow status summary for multiple workflows
   * Requirements: 需求 2.1, 需求 2.2, 需求 6.3
   */
  static async getWorkflowStatusSummaries(workflowIds: string[]): Promise<WorkflowStatusSummary[]> {
    if (workflowIds.length === 0) return [];

    return this.withConnection(async (connection) => {
      const placeholders = workflowIds.map(() => '?').join(',');

      const [results] = await connection.execute(
        `SELECT 
          w.workflow_id,
          w.status,
          w.progress,
          w.current_stage,
          COUNT(CASE WHEN ar.analyst_type = 'fundamental' AND ar.status = 'completed' THEN 1 END) > 0 as fundamental_completed,
          COUNT(CASE WHEN ar.analyst_type = 'technical' AND ar.status = 'completed' THEN 1 END) > 0 as technical_completed,
          COUNT(CASE WHEN ar.analyst_type = 'sentiment' AND ar.status = 'completed' THEN 1 END) > 0 as sentiment_completed,
          COUNT(CASE WHEN ar.analyst_type = 'news' AND ar.status = 'completed' THEN 1 END) > 0 as news_completed,
          COUNT(CASE WHEN rr.researcher_type = 'bull' AND rr.status = 'completed' THEN 1 END) > 0 as bull_completed,
          COUNT(CASE WHEN rr.researcher_type = 'bear' AND rr.status = 'completed' THEN 1 END) > 0 as bear_completed,
          COUNT(fd.decision_id) > 0 as final_decision_completed
         FROM workflows w
         LEFT JOIN analyst_reports ar ON w.workflow_id = ar.workflow_id
         LEFT JOIN research_reports rr ON w.workflow_id = rr.workflow_id
         LEFT JOIN final_decisions fd ON w.workflow_id = fd.workflow_id
         WHERE w.workflow_id IN (${placeholders})
         GROUP BY w.workflow_id, w.status, w.progress, w.current_stage`,
        workflowIds
      );

      return (results as any[]).map((row) => ({
        workflow_id: row.workflow_id,
        status: row.status,
        progress: row.progress,
        current_stage: row.current_stage,
        analyst_completion: {
          fundamental: Boolean(row.fundamental_completed),
          technical: Boolean(row.technical_completed),
          sentiment: Boolean(row.sentiment_completed),
          news: Boolean(row.news_completed),
        },
        research_completion: {
          bull: Boolean(row.bull_completed),
          bear: Boolean(row.bear_completed),
        },
        final_decision_completed: Boolean(row.final_decision_completed),
      }));
    });
  }

  // ============================================================================
  // Enhanced Query Methods
  // ============================================================================

  /**
   * Get analysis reports with filtering and pagination
   * Requirements: 需求 4.1, 需求 4.2, 需求 4.3
   */
  static async getAnalysisReports(
    workflowId: string,
    type?: 'analyst' | 'research' | 'all'
  ): Promise<{ analystReports: AnalystReport[]; researchReports: ResearchReport[] }> {
    return this.withConnection(async (connection) => {
      let analystReports: AnalystReport[] = [];
      let researchReports: ResearchReport[] = [];

      if (type === 'analyst' || type === 'all' || !type) {
        const [analystResults] = await connection.execute(
          `SELECT ar.*, 
                  tad.trading_signal, tad.trend_signal, tad.support_level, tad.resistance_level,
                  tad.stop_loss_level, tad.target_price, tad.rsi_value, tad.macd_signal, tad.key_levels,
                  sad.overall_sentiment, sad.sentiment_score, sad.positive_news_count, 
                  sad.negative_news_count, sad.neutral_news_count, sad.key_drivers
           FROM analyst_reports ar
           LEFT JOIN technical_analysis_details tad ON ar.report_id = tad.report_id
           LEFT JOIN sentiment_analysis_details sad ON ar.report_id = sad.report_id
           WHERE ar.workflow_id = ? AND ar.status = 'completed'
           ORDER BY ar.created_at`,
          [workflowId]
        );
        analystReports = analystResults as AnalystReport[];
      }

      if (type === 'research' || type === 'all' || !type) {
        const [researchResults] = await connection.execute(
          `SELECT rr.*, 
                  GROUP_CONCAT(
                    CONCAT(ra.argument_type, ':', ra.content, '|', COALESCE(ra.strength_score, 0))
                    ORDER BY ra.sequence_order SEPARATOR ';;'
                  ) as arguments_data
           FROM research_reports rr
           LEFT JOIN research_arguments ra ON rr.report_id = ra.report_id
           WHERE rr.workflow_id = ? AND rr.status = 'completed'
           GROUP BY rr.report_id
           ORDER BY rr.created_at`,
          [workflowId]
        );
        researchReports = researchResults as ResearchReport[];
      }

      return { analystReports, researchReports };
    });
  }

  /**
   * Enhanced workflow query with advanced filtering
   * Requirements: 需求 4.1, 需求 4.2, 需求 6.1
   */
  static async queryWorkflowsEnhanced(
    options: WorkflowQueryOptions & {
      sort_by?: string;
      sort_order?: 'asc' | 'desc';
      page?: number;
      include_stats?: boolean;
    } = {}
  ): Promise<{
    workflows: Workflow[];
    total: number;
    page: number;
    limit: number;
  }> {
    return this.withConnection(async (connection) => {
      let query = `
        SELECT w.*,
               ${
                 options.include_stats
                   ? `
               (SELECT COUNT(*) FROM analyst_reports ar WHERE ar.workflow_id = w.workflow_id AND ar.status = 'completed') as completed_reports,
               (SELECT COUNT(*) FROM workflow_events we WHERE we.workflow_id = w.workflow_id AND we.event_type = 'error') as error_count,
               `
                   : ''
               }
               1 as placeholder
        FROM workflows w
        WHERE 1=1
      `;

      const params: any[] = [];
      const countParams: any[] = [];

      // Build WHERE clause
      if (options.ticker) {
        query += ' AND w.ticker = ?';
        params.push(options.ticker);
        countParams.push(options.ticker);
      }

      if (options.status && options.status.length > 0) {
        query += ` AND w.status IN (${options.status.map(() => '?').join(',')})`;
        params.push(...options.status);
        countParams.push(...options.status);
      }

      if (options.date_from) {
        query += ' AND w.created_at >= ?';
        params.push(options.date_from);
        countParams.push(options.date_from);
      }

      if (options.date_to) {
        query += ' AND w.created_at <= ?';
        params.push(options.date_to);
        countParams.push(options.date_to);
      }

      // Get total count
      const countQuery =
        `SELECT COUNT(*) as total FROM workflows w WHERE 1=1` +
        query.substring(
          query.indexOf('WHERE 1=1') + 9,
          query.indexOf('ORDER BY') > 0 ? query.indexOf('ORDER BY') : query.length
        );

      const [countResult] = await connection.execute(
        countQuery.replace(/ORDER BY.*$/, '').replace(/LIMIT.*$/, ''),
        countParams
      );
      const total = (countResult as any[])[0].total;

      // Add sorting
      const sortBy = options.sort_by || 'created_at';
      const sortOrder = options.sort_order || 'desc';
      query += ` ORDER BY w.${sortBy} ${sortOrder.toUpperCase()}`;

      // Add pagination
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;

      query += ' LIMIT ? OFFSET ?';
      params.push(limit, offset);

      const [workflows] = await connection.execute(query, params);

      return {
        workflows: workflows as Workflow[],
        total,
        page,
        limit,
      };
    });
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Check if workflow exists and is accessible
   * Requirements: 需求 7.1, 需求 7.2
   */
  static async validateWorkflowAccess(workflowId: string, userId?: string): Promise<boolean> {
    return this.withConnection(async (connection) => {
      const [results] = await connection.execute(
        'SELECT COUNT(*) as count FROM workflows WHERE workflow_id = ?',
        [workflowId]
      );

      return (results as any[])[0].count > 0;
    });
  }

  /**
   * Get workflow performance metrics
   * Requirements: 需求 6.1, 需求 6.7
   */
  static async getWorkflowPerformanceMetrics(workflowId: string): Promise<{
    total_duration_seconds: number;
    analyst_durations: Record<string, number>;
    event_counts: Record<string, number>;
  }> {
    return this.withConnection(async (connection) => {
      // Get overall duration
      const [workflowData] = await connection.execute(
        `SELECT 
          TIMESTAMPDIFF(SECOND, started_at, completed_at) as total_duration_seconds
         FROM workflows 
         WHERE workflow_id = ?`,
        [workflowId]
      );

      // Get analyst execution times
      const [analystData] = await connection.execute(
        `SELECT analyst_type, execution_time_ms
         FROM analyst_reports 
         WHERE workflow_id = ? AND execution_time_ms IS NOT NULL`,
        [workflowId]
      );

      // Get event counts by type
      const [eventData] = await connection.execute(
        `SELECT event_type, COUNT(*) as count
         FROM workflow_events 
         WHERE workflow_id = ?
         GROUP BY event_type`,
        [workflowId]
      );

      const analystDurations: Record<string, number> = {};
      (analystData as any[]).forEach((row) => {
        analystDurations[row.analyst_type] = row.execution_time_ms;
      });

      const eventCounts: Record<string, number> = {};
      (eventData as any[]).forEach((row) => {
        eventCounts[row.event_type] = row.count;
      });

      return {
        total_duration_seconds: (workflowData as any[])[0]?.total_duration_seconds || 0,
        analyst_durations: analystDurations,
        event_counts: eventCounts,
      };
    });
  }

  // ============================================================================
  // Data Consistency and Integrity Methods
  // ============================================================================

  /**
   * Validate data integrity for a workflow
   * Requirements: 需求 7.1, 需求 7.2
   */
  static async validateWorkflowIntegrity(workflowId: string): Promise<{
    isValid: boolean;
    issues: string[];
  }> {
    return this.withConnection(async (connection) => {
      const issues: string[] = [];

      // Check if workflow exists
      const [workflowRows] = await connection.execute(
        'SELECT COUNT(*) as count FROM workflows WHERE workflow_id = ?',
        [workflowId]
      );

      if ((workflowRows as any[])[0].count === 0) {
        issues.push('工作流不存在');
        return { isValid: false, issues };
      }

      // Check analyst reports consistency
      const [analystRows] = await connection.execute(
        'SELECT analyst_type, COUNT(*) as count FROM analyst_reports WHERE workflow_id = ? GROUP BY analyst_type',
        [workflowId]
      );

      const expectedAnalysts = ['fundamental', 'technical', 'sentiment', 'news'];
      const existingAnalysts = (analystRows as any[]).map((row) => row.analyst_type);

      for (const analyst of expectedAnalysts) {
        if (!existingAnalysts.includes(analyst)) {
          issues.push(`缺少${analyst}分析师报告`);
        }
      }

      // Check for orphaned records
      const [orphanedReports] = await connection.execute(
        `SELECT COUNT(*) as count FROM analyst_reports ar 
         WHERE ar.workflow_id = ? AND NOT EXISTS (
           SELECT 1 FROM workflows w WHERE w.workflow_id = ar.workflow_id
         )`,
        [workflowId]
      );

      if ((orphanedReports as any[])[0].count > 0) {
        issues.push('存在孤立的分析报告记录');
      }

      return {
        isValid: issues.length === 0,
        issues,
      };
    });
  }

  /**
   * Clean up incomplete or failed workflows
   * Requirements: 需求 7.2
   */
  static async cleanupFailedWorkflows(olderThanDays: number = 7): Promise<number> {
    return this.withConnection(async (connection) => {
      await connection.beginTransaction();
      try {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

        // Get failed workflows older than cutoff
        const [failedWorkflows] = await connection.execute(
          `SELECT workflow_id FROM workflows 
           WHERE status = 'failed' AND created_at < ?`,
          [cutoffDate]
        );

        let cleanedCount = 0;

        for (const workflow of failedWorkflows as any[]) {
          const workflowId = workflow.workflow_id;

          // Delete related records in order
          await connection.execute('DELETE FROM workflow_events WHERE workflow_id = ?', [
            workflowId,
          ]);
          await connection.execute('DELETE FROM workflow_state_snapshots WHERE workflow_id = ?', [
            workflowId,
          ]);
          await connection.execute(
            'DELETE FROM technical_analysis_details WHERE report_id IN (SELECT report_id FROM analyst_reports WHERE workflow_id = ?)',
            [workflowId]
          );
          await connection.execute(
            'DELETE FROM sentiment_analysis_details WHERE report_id IN (SELECT report_id FROM analyst_reports WHERE workflow_id = ?)',
            [workflowId]
          );
          await connection.execute(
            'DELETE FROM research_arguments WHERE report_id IN (SELECT report_id FROM research_reports WHERE workflow_id = ?)',
            [workflowId]
          );
          await connection.execute('DELETE FROM analyst_reports WHERE workflow_id = ?', [
            workflowId,
          ]);
          await connection.execute('DELETE FROM research_reports WHERE workflow_id = ?', [
            workflowId,
          ]);
          await connection.execute('DELETE FROM final_decisions WHERE workflow_id = ?', [
            workflowId,
          ]);
          await connection.execute('DELETE FROM workflows WHERE workflow_id = ?', [workflowId]);

          cleanedCount++;
        }

        await connection.commit();
        return cleanedCount;
      } catch (error) {
        await connection.rollback();
        throw error;
      }
    });
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  private static generateId(prefix: string): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
}

export default EnhancedLangGraphDatabase;
