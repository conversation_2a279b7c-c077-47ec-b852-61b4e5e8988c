// LangGraph 数据库结构测试脚本
const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'trading_analysis',
  charset: 'utf8mb4',
  timezone: '+00:00',
};

async function testLangGraphDatabase() {
  let connection;
  
  try {
    console.log('🔗 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 测试工作流实例创建
    console.log('\n📝 测试工作流实例创建...');
    const workflow_id = `wf_${Date.now()}`;
    const task_id = `task_${Date.now()}`;
    const thread_id = `thread_${Date.now()}`;
    
    await connection.execute(
      'CALL CreateWorkflowInstance(?, ?, ?, ?, ?, ?)',
      [
        workflow_id,
        task_id,
        thread_id,
        'AAPL',
        new Date().toISOString().split('T')[0],
        JSON.stringify({
          deep_think_llm: 'gpt-4o',
          quick_think_llm: 'gpt-4o-mini',
          max_debate_rounds: 3,
          research_depth: 'standard',
          online_tools: true
        })
      ]
    );
    console.log('✅ 工作流实例创建成功:', workflow_id);

    // 2. 测试分析师结果保存
    console.log('\n📊 测试分析师结果保存...');
    const analystTypes = ['fundamental', 'technical', 'sentiment', 'news'];
    
    for (const analystType of analystTypes) {
      await connection.execute(
        `INSERT INTO analysis_results 
         (workflow_id, analyst_type, analysis_result, summary, key_findings, confidence_score, status) 
         VALUES (?, ?, ?, ?, ?, ?, 'completed')`,
        [
          workflow_id,
          analystType,
          JSON.stringify({
            analysis: `${analystType} analysis result`,
            data: { score: Math.random() * 100 }
          }),
          `${analystType} 分析摘要`,
          JSON.stringify({ finding1: 'key finding', finding2: 'another finding' }),
          Math.random()
        ]
      );
      console.log(`✅ ${analystType} 分析师结果保存成功`);
    }

    // 3. 测试研究员结果保存
    console.log('\n🔍 测试研究员结果保存...');
    const researcherTypes = ['bull', 'bear'];
    
    for (const researcherType of researcherTypes) {
      await connection.execute(
        `INSERT INTO researcher_results 
         (workflow_id, researcher_type, research_result, arguments, evidence, confidence_level, target_price, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, 'completed')`,
        [
          workflow_id,
          researcherType,
          JSON.stringify({
            research: `${researcherType} research result`,
            recommendation: researcherType === 'bull' ? 'buy' : 'sell'
          }),
          JSON.stringify({ arg1: 'strong argument', arg2: 'supporting point' }),
          JSON.stringify({ evidence1: 'market data', evidence2: 'financial metrics' }),
          Math.random(),
          150 + Math.random() * 50
        ]
      );
      console.log(`✅ ${researcherType} 研究员结果保存成功`);
    }

    // 4. 测试辩论记录保存
    console.log('\n💬 测试辩论记录保存...');
    const debateRounds = 3;
    let sequenceOrder = 1;
    
    for (let round = 1; round <= debateRounds; round++) {
      // Bull 论点
      await connection.execute(
        `INSERT INTO debate_records 
         (workflow_id, debate_round, participant_type, argument, supporting_evidence, strength_score, sequence_order) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          workflow_id,
          round,
          'bull',
          `多头论点 - 第${round}轮: 股价将上涨`,
          JSON.stringify({ data: 'positive earnings', trend: 'upward' }),
          Math.random(),
          sequenceOrder++
        ]
      );

      // Bear 论点
      await connection.execute(
        `INSERT INTO debate_records 
         (workflow_id, debate_round, participant_type, argument, supporting_evidence, strength_score, sequence_order) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          workflow_id,
          round,
          'bear',
          `空头论点 - 第${round}轮: 股价将下跌`,
          JSON.stringify({ data: 'market volatility', risk: 'high' }),
          Math.random(),
          sequenceOrder++
        ]
      );
      
      console.log(`✅ 第${round}轮辩论记录保存成功`);
    }

    // 5. 测试共识评估保存
    console.log('\n🤝 测试共识评估保存...');
    await connection.execute(
      `INSERT INTO consensus_evaluations 
       (workflow_id, bull_strength, bear_strength, consensus_direction, consensus_confidence, key_agreements, key_disagreements, synthesis_summary, total_debate_rounds) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        workflow_id,
        0.7,
        0.6,
        'bullish',
        0.65,
        JSON.stringify({ agreement1: 'strong fundamentals', agreement2: 'good market position' }),
        JSON.stringify({ disagreement1: 'valuation concerns', disagreement2: 'market timing' }),
        '经过充分辩论，多头观点略占优势，建议谨慎看多',
        debateRounds
      ]
    );
    console.log('✅ 共识评估结果保存成功');

    // 6. 测试风险评估保存
    console.log('\n⚠️ 测试风险评估保存...');
    await connection.execute(
      `INSERT INTO risk_assessments 
       (workflow_id, overall_risk_level, market_risk, company_risk, liquidity_risk, volatility_risk, risk_factors, mitigation_strategies, stop_loss_recommendation, risk_summary) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        workflow_id,
        'medium',
        0.6,
        0.4,
        0.3,
        0.7,
        JSON.stringify({ factor1: 'market volatility', factor2: 'sector rotation' }),
        JSON.stringify({ strategy1: 'diversification', strategy2: 'position sizing' }),
        140.50,
        '整体风险可控，建议设置合理止损位'
      ]
    );
    console.log('✅ 风险评估结果保存成功');

    // 7. 测试交易决策保存
    console.log('\n📈 测试交易决策保存...');
    await connection.execute(
      `INSERT INTO trading_decisions 
       (workflow_id, decision_type, confidence_level, target_price, entry_price_range, stop_loss_price, take_profit_price, position_size_percentage, time_horizon, decision_rationale) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        workflow_id,
        'buy',
        0.75,
        180.00,
        JSON.stringify({ min: 145.00, max: 150.00 }),
        140.00,
        180.00,
        5.0,
        '3-6 months',
        '基于综合分析，建议在当前价位买入，目标价位180美元'
      ]
    );
    console.log('✅ 交易决策保存成功');

    // 8. 测试工作流消息保存
    console.log('\n💬 测试工作流消息保存...');
    const stages = ['data_collection', 'fundamental_analyst', 'technical_analyst', 'consensus_evaluator', 'final_decision'];
    
    for (let i = 0; i < stages.length; i++) {
      const message_id = `msg_${Date.now()}_${i}`;
      await connection.execute(
        `INSERT INTO workflow_messages 
         (workflow_id, message_id, message_type, sender, content, sequence_number, stage_name) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          workflow_id,
          message_id,
          'system',
          stages[i],
          `阶段 ${stages[i]} 执行完成`,
          i + 1,
          stages[i]
        ]
      );
    }
    console.log('✅ 工作流消息保存成功');

    // 9. 测试状态快照保存
    console.log('\n📸 测试状态快照保存...');
    await connection.execute(
      'CALL SaveWorkflowSnapshot(?, ?, ?, ?)',
      [
        workflow_id,
        'final_state',
        JSON.stringify({
          ticker: 'AAPL',
          analysis: { fundamental: 'completed', technical: 'completed' },
          research: { bull: 'completed', bear: 'completed' },
          decision: { type: 'buy', confidence: 0.75 }
        }),
        `checkpoint_${Date.now()}`
      ]
    );
    console.log('✅ 状态快照保存成功');

    // 10. 更新工作流状态为完成
    console.log('\n🔄 测试工作流状态更新...');
    await connection.execute(
      'CALL UpdateWorkflowStage(?, ?, ?, ?)',
      [workflow_id, 'completed', 100, 'completed']
    );
    console.log('✅ 工作流状态更新成功');

    // 11. 测试视图查询
    console.log('\n📋 测试工作流完整状态视图...');
    const [statusRows] = await connection.execute(
      'SELECT * FROM workflow_complete_status WHERE workflow_id = ?',
      [workflow_id]
    );
    
    if (statusRows.length > 0) {
      const status = statusRows[0];
      console.log('✅ 工作流完整状态查询成功:');
      console.log(`   - 工作流ID: ${status.workflow_id}`);
      console.log(`   - 股票代码: ${status.ticker}`);
      console.log(`   - 当前阶段: ${status.current_stage}`);
      console.log(`   - 状态: ${status.workflow_status}`);
      console.log(`   - 进度: ${status.progress}%`);
      console.log(`   - 基本面分析: ${status.fundamental_completed ? '✅' : '❌'}`);
      console.log(`   - 技术分析: ${status.technical_completed ? '✅' : '❌'}`);
      console.log(`   - 情绪分析: ${status.sentiment_completed ? '✅' : '❌'}`);
      console.log(`   - 新闻分析: ${status.news_completed ? '✅' : '❌'}`);
      console.log(`   - 多头研究: ${status.bull_completed ? '✅' : '❌'}`);
      console.log(`   - 空头研究: ${status.bear_completed ? '✅' : '❌'}`);
      console.log(`   - 共识评估: ${status.consensus_completed ? '✅' : '❌'}`);
      console.log(`   - 风险评估: ${status.risk_assessment_completed ? '✅' : '❌'}`);
      console.log(`   - 最终决策: ${status.decision_completed ? '✅' : '❌'}`);
      console.log(`   - 辩论消息数: ${status.total_debate_messages}`);
      console.log(`   - 最大辩论轮次: ${status.max_debate_round}`);
      console.log(`   - 总消息数: ${status.total_workflow_messages}`);
      console.log(`   - 执行时长: ${status.duration_seconds}秒`);
    }

    // 12. 测试数据查询
    console.log('\n🔍 测试数据查询功能...');
    
    // 查询分析师结果
    const [analystResults] = await connection.execute(
      'SELECT analyst_type, status, confidence_score FROM analysis_results WHERE workflow_id = ?',
      [workflow_id]
    );
    console.log(`✅ 分析师结果查询成功: ${analystResults.length} 条记录`);

    // 查询研究员结果
    const [researcherResults] = await connection.execute(
      'SELECT researcher_type, status, confidence_level, target_price FROM researcher_results WHERE workflow_id = ?',
      [workflow_id]
    );
    console.log(`✅ 研究员结果查询成功: ${researcherResults.length} 条记录`);

    // 查询辩论记录
    const [debateResults] = await connection.execute(
      'SELECT debate_round, participant_type, strength_score FROM debate_records WHERE workflow_id = ? ORDER BY debate_round, sequence_order',
      [workflow_id]
    );
    console.log(`✅ 辩论记录查询成功: ${debateResults.length} 条记录`);

    console.log('\n🎉 所有测试通过！LangGraph 数据库结构工作正常');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
if (require.main === module) {
  testLangGraphDatabase().catch(console.error);
}

module.exports = { testLangGraphDatabase };