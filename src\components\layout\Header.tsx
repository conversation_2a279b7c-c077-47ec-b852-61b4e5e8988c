'use client';

import { authApi } from '@/lib/api';
import useUserStore from '@/store/userStore';
import { Bars3Icon, ChartBarIcon, UserCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export function Header() {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, loading, fetchUser, clearUser } = useUserStore();

  const handleLogout = async () => {
    try {
      await authApi.logout();
      // 清除用户状态和会话信息
      clearUser();
      router.push('/login');
      router.refresh();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleQuickStart = () => {
    if (user) {
      router.push('/create-task');
    } else {
      router.push('/login');
    }
  };

  const publicNavigation = [
    { name: '功能特性', href: '/#features' },
    { name: '案例展示', href: '/#examples' },
    { name: '文档', href: '/#docs' },
  ];

  const protectedNavigation = [{ name: '任务列表', href: '/tasks' }];

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  if (loading) {
    return null;
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <ChartBarIcon className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-slate-900 dark:text-white">TradingAgents</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {publicNavigation.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
              >
                {item.name}
              </a>
            ))}
            {user &&
              protectedNavigation.map((item) => (
                <a
                  key={item.name}
                  onClick={() => router.push(item.href)}
                  className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                >
                  {item.name}
                </a>
              ))}

            {user ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-slate-600 dark:text-slate-300">
                  欢迎, {user.username}
                </span>
                <div className="relative group">
                  <button className="flex items-center space-x-1 text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors">
                    <UserCircleIcon className="h-6 w-6" />
                  </button>
                  <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-md shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                      个人资料
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  href="/login"
                  className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                >
                  登录
                </Link>
                <Link
                  href="/register"
                  className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                >
                  注册
                </Link>
              </div>
            )}

            <button
              onClick={handleQuickStart}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              {user ? '创建任务' : '快速开始'}
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-slate-200 dark:border-slate-700 py-4">
            <div className="space-y-4">
              {publicNavigation.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              {user &&
                protectedNavigation.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                ))}

              {user ? (
                <>
                  <div className="border-t border-slate-200 dark:border-slate-700 pt-4">
                    <p className="text-sm text-slate-600 dark:text-slate-300 px-4">
                      欢迎, {user.username}
                    </p>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      个人资料
                    </Link>
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMobileMenuOpen(false);
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    >
                      退出登录
                    </button>
                  </div>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    登录
                  </Link>
                  <Link
                    href="/register"
                    className="block text-slate-600 dark:text-slate-300 hover:text-blue-600 transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    注册
                  </Link>
                </>
              )}

              <button
                onClick={() => {
                  handleQuickStart();
                  setIsMobileMenuOpen(false);
                }}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-left"
              >
                {user ? '创建任务' : '快速开始'}
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
