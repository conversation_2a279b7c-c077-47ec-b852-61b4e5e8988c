// LangGraph 工作流状态查询 API
import LangGraphDatabase from '@/lib/langgraph-database';
import { NextRequest, NextResponse } from 'next/server';

// GET - 获取工作流完整状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflow_id = searchParams.get('workflow_id');
    const task_id = searchParams.get('task_id');

    if (!workflow_id && !task_id) {
      return NextResponse.json(
        { success: false, message: '需要提供 workflow_id 或 task_id' },
        { status: 400 }
      );
    }

    let workflowId = workflow_id;

    // 如果只提供了 task_id，构造 workflow_id
    if (!workflowId && task_id) {
      workflowId = `wf_${task_id}`;
    }

    // 获取工作流基本信息
    const workflow = await LangGraphDatabase.getWorkflow(workflowId!);

    if (!workflow) {
      return NextResponse.json({ success: false, message: '工作流不存在' }, { status: 404 });
    }

    const response = {
      success: true,
      data: {
        workflow,
        // 基本状态信息
        status: {
          workflow_id: workflow.workflow_id,
          current_stage: workflow.current_stage,
          status: workflow.status,
          progress: workflow.progress,
          created_at: workflow.created_at,
          updated_at: workflow.updated_at,
          error_message: workflow.error_message,
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('获取工作流状态失败:', error);
    return NextResponse.json({ success: false, message: '服务器内部错误' }, { status: 500 });
  }
}
