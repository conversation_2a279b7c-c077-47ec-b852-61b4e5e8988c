# 枚举映射使用指南

## 概述

本项目采用了前端映射的方式来实现中英文枚举值的转换：

- **数据库和 API**: 使用英文枚举值（如 `'pending'`, `'running'`, `'completed'`）
- **前端显示**: 通过映射函数显示中文（如 `'待处理'`, `'运行中'`, `'已完成'`）

这种方式的优势：

1. 保持数据库和 API 的国际化兼容性
2. 便于与第三方系统集成
3. 支持多语言扩展
4. 数据迁移和维护更简单

## 映射文件

所有的映射定义都在 `src/utils/enums.ts` 文件中：

```typescript
// 任务状态映射
export const TASK_STATUS_MAP = {
  pending: '待处理',
  running: '运行中',
  completed: '已完成',
  failed: '失败',
  cancelled: '已取消',
} as const;

// 获取中文标签
export function getTaskStatusLabel(value: string): string {
  return TASK_STATUS_MAP[value as keyof typeof TASK_STATUS_MAP] || value;
}
```

## 使用方法

### 1. 在组件中显示中文

```typescript
import { getTaskStatusLabel, getResearchDepthLabel } from '@/utils/enums';

function TaskList({ tasks }) {
  return (
    <div>
      {tasks.map((task) => (
        <div key={task.id}>
          <span>状态: {getTaskStatusLabel(task.status)}</span>
          <span>深度: {getResearchDepthLabel(task.research_depth)}</span>
        </div>
      ))}
    </div>
  );
}
```

### 2. 使用 EnumDisplay 组件

```typescript
import { EnumDisplay } from '@/components/ui/EnumDisplay';

function TaskCard({ task }) {
  return (
    <div>
      <EnumDisplay type="taskStatus" value={task.status} />
      <EnumDisplay type="riskLevel" value={task.risk_level} />
    </div>
  );
}
```

### 3. 在表单中使用选项

```typescript
import { taskStatusOptions, researchDepthOptions } from '@/utils/enums';

function TaskForm() {
  return (
    <form>
      <select>
        {taskStatusOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </form>
  );
}
```

## 支持的枚举类型

### 1. 研究深度 (Research Depth)

- `shallow` → 浅层分析
- `medium` → 中等分析
- `deep` → 深度分析
- `quick` → 快速分析
- `standard` → 标准分析

### 2. 分析周期 (Analysis Period)

- `1d` → 1 天
- `1w` → 1 周
- `1m` → 1 个月
- `3m` → 3 个月
- `6m` → 6 个月
- `1y` → 1 年
- `custom` → 自定义

### 3. 任务状态 (Task Status)

- `pending` → 待处理
- `running` → 运行中
- `completed` → 已完成
- `failed` → 失败
- `cancelled` → 已取消

### 4. 代理状态 (Agent Status)

- `idle` → 空闲
- `initializing` → 初始化中
- `waiting` → 等待中
- `active` → 活跃
- `running` → 运行中
- `completed` → 已完成
- `error` → 错误
- `failed` → 失败

### 5. 交易行动 (Trading Actions)

- `buy` → 买入
- `sell` → 卖出
- `hold` → 持有

### 6. 风险等级 (Risk Levels)

- `low` → 低风险
- `medium` → 中等风险
- `high` → 高风险
- `very_high` → 极高风险

### 7. 其他枚举

- 消息类型: `human` → 用户, `ai` → AI, `system` → 系统, `tool` → 工具
- 系统状态: `healthy` → 健康, `degraded` → 降级, `down` → 停机
- 情绪分析: `positive` → 积极, `negative` → 消极, `neutral` → 中性

## 添加新的枚举映射

1. 在 `src/utils/enums.ts` 中添加映射对象：

```typescript
export const NEW_ENUM_MAP = {
  value1: '中文标签1',
  value2: '中文标签2',
} as const;
```

2. 添加获取函数：

```typescript
export function getNewEnumLabel(value: string): string {
  return NEW_ENUM_MAP[value as keyof typeof NEW_ENUM_MAP] || value;
}
```

3. 添加选项数组（如果需要）：

```typescript
export const newEnumOptions = Object.entries(NEW_ENUM_MAP).map(([value, label]) => ({
  value,
  label,
}));
```

4. 在 `EnumDisplay` 组件中添加支持（如果需要特殊样式）。

## 最佳实践

1. **保持一致性**: 所有新的枚举都应该遵循这种映射模式
2. **类型安全**: 使用 TypeScript 的 `as const` 确保类型安全
3. **回退处理**: 映射函数应该在找不到映射时返回原值
4. **性能考虑**: 映射对象是常量，查找性能很好
5. **可维护性**: 所有映射集中在一个文件中，便于维护

## 国际化扩展

如果将来需要支持多语言，可以扩展映射结构：

```typescript
const TASK_STATUS_I18N = {
  pending: {
    'zh-CN': '待处理',
    'en-US': 'Pending',
    'ja-JP': '保留中',
  },
  // ...
};

export function getTaskStatusLabel(value: string, locale = 'zh-CN'): string {
  return TASK_STATUS_I18N[value]?.[locale] || value;
}
```

## 注意事项

1. **数据库迁移**: 如果需要修改枚举值，确保同时更新数据库架构
2. **API 兼容性**: 外部 API 调用仍然使用英文枚举值
3. **测试覆盖**: 确保映射函数有充分的测试覆盖
4. **文档同步**: 修改枚举时记得更新相关文档
