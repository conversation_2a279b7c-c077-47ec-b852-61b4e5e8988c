# 数据库连接问题修复指南

## 问题描述

错误信息：`Error: connect ECONNREFUSED ::1:3306`

这表示应用尝试连接到端口 3306，但实际 MySQL 运行在端口 13306。

## 已修复的配置

### 1. 环境变量配置 (`.env.local`)

```env
# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=13306
DB_USER=root
DB_PASSWORD=12345678
DB_NAME=trading_analysis
```

### 2. MCP 配置 (`.kiro/settings/mcp.json`)

```json
{
  "env": {
    "MYSQL_HOST": "127.0.0.1",
    "MYSQL_PORT": "13306",
    "MYSQL_USER": "root",
    "MYSQL_PASS": "root"
  }
}
```

### 3. 数据库连接代码 (`src/lib/db.ts`)

```typescript
const pool = mysql.createPool({
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '12345678',
  database: process.env.DB_NAME || 'trading_analysis',
  port: Number(process.env.DB_PORT) || 13306, // 默认13306
  // ...
});
```

## 测试步骤

### 1. 测试数据库连接

```bash
node test-db-connection.js
```

### 2. 设置数据库（如果需要）

```bash
node setup-database.js
```

### 3. 重启开发服务器

```bash
npm run dev
```

## 常见问题排查

### 问题 1: 连接被拒绝 (ECONNREFUSED)

**可能原因:**

- MySQL 服务未运行
- 端口配置错误
- 防火墙阻止连接

**解决方案:**

1. 检查 MySQL 服务状态
2. 确认端口 13306 是否开放
3. 尝试使用 localhost 替代 127.0.0.1

### 问题 2: 访问被拒绝 (ER_ACCESS_DENIED_ERROR)

**可能原因:**

- 用户名或密码错误
- 用户权限不足

**解决方案:**

1. 检查用户名和密码
2. 确保用户有数据库访问权限

### 问题 3: 数据库不存在 (ER_BAD_DB_ERROR)

**可能原因:**

- 数据库未创建

**解决方案:**

1. 运行数据库设置脚本
2. 手动创建数据库：`CREATE DATABASE trading_analysis;`

## MySQL 服务管理

### Windows

```bash
# 启动MySQL服务
net start mysql

# 停止MySQL服务
net stop mysql

# 检查服务状态
sc query mysql
```

### macOS (使用 Homebrew)

```bash
# 启动MySQL服务
brew services start mysql

# 停止MySQL服务
brew services stop mysql

# 检查服务状态
brew services list | grep mysql
```

### Linux (Ubuntu/Debian)

```bash
# 启动MySQL服务
sudo systemctl start mysql

# 停止MySQL服务
sudo systemctl stop mysql

# 检查服务状态
sudo systemctl status mysql
```

## Docker 环境

如果使用 Docker 运行 MySQL：

```bash
# 检查容器状态
docker ps | grep mysql

# 启动MySQL容器
docker-compose up mysql -d

# 查看容器日志
docker-compose logs mysql

# 连接到MySQL容器
docker exec -it tradingagents-mysql mysql -u root -p
```

## 验证连接

成功连接后，你应该看到：

```
✅ 数据库连接成功！
✅ 测试查询成功: [ { test: 1 } ]
📋 可用数据库: [ 'information_schema', 'mysql', 'performance_schema', 'sys', 'trading_analysis' ]
📋 数据库表: [ { Tables_in_trading_analysis: 'tasks' }, ... ]
```

## 环境变量检查

在应用启动时，检查控制台输出：

```
DB_HOST: 127.0.0.1
DB_USER: root
DB_PASSWORD: 12345678
DB_NAME: trading_analysis
DB_PORT: 13306
```

如果环境变量显示为 `undefined`，说明 `.env.local` 文件未正确加载。

## 下一步

1. 运行测试脚本确认连接正常
2. 重启开发服务器
3. 访问应用，检查是否还有连接错误
4. 如果问题持续，检查应用日志获取更多信息

## 联系支持

如果问题仍然存在，请提供：

1. 错误日志的完整信息
2. 环境变量的值（隐藏密码）
3. MySQL 服务的运行状态
4. 操作系统信息
