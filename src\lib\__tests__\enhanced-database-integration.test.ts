// Enhanced Database Integration Tests
// Tests to verify the enhanced database integrates properly with existing system

import { describe, expect, it } from '@jest/globals';

describe('Enhanced Database Integration', () => {
  it('should export all required types and classes', async () => {
    // Test that we can import the enhanced database
    const EnhancedLangGraphDatabase = await import('../enhanced-langgraph-database');

    expect(EnhancedLangGraphDatabase.default).toBeDefined();
    expect(EnhancedLangGraphDatabase.EnhancedLangGraphDatabase).toBeDefined();
  });

  it('should have all required methods', async () => {
    const { EnhancedLangGraphDatabase } = await import('../enhanced-langgraph-database');

    // Check that all enhanced methods exist
    expect(typeof EnhancedLangGraphDatabase.createCompleteWorkflow).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getCompleteWorkflowStatus).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.batchUpdateAgentStates).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.batchSaveAnalystResults).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getAnalysisStatistics).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getWorkflowStatusSummaries).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getAnalysisReports).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.queryWorkflowsEnhanced).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.validateWorkflowAccess).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getWorkflowPerformanceMetrics).toBe('function');

    // Check that parent methods are still available
    expect(typeof EnhancedLangGraphDatabase.createWorkflow).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.getWorkflow).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.updateWorkflowStatus).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.saveAnalystReport).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.saveResearchReport).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.saveFinalDecision).toBe('function');
    expect(typeof EnhancedLangGraphDatabase.logWorkflowEvent).toBe('function');
  });

  it('should export all required types', async () => {
    // Test that we can import the types
    const types = await import('../enhanced-langgraph-database');

    // These should be available as type exports (we can't test types directly in runtime,
    // but we can test that the module exports them without errors)
    expect(types).toBeDefined();
  });

  it('should be compatible with existing database types', async () => {
    const dbTypes = await import('../../types/langgraph-database');
    const enhancedTypes = await import('../enhanced-langgraph-database');

    // Both modules should be importable without conflicts
    expect(dbTypes).toBeDefined();
    expect(enhancedTypes).toBeDefined();
  });
});
