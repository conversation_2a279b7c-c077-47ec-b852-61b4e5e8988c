# Docker 国内镜像加速器配置
#
# 重要说明：
# 1. 推荐使用镜像加速器而不是直接修改镜像名称
# 2. 镜像加速器需要在 Docker Daemon 中配置（见 daemon.json）
# 3. 配置后可直接使用官方镜像名称，如 mysql:8.0.28
#
# 主要镜像加速器：
# - 阿里云: https://xxx.mirror.aliyuncs.com (需要登录获取专属地址)
# - 网易: https://hub-mirror.c.163.com
# - 腾讯云: https://ccr.ccs.tencentyun.com
# - DaoCloud: https://docker.m.daocloud.io

services:
  # AKShare 后端数据服务
  akshare-backend:
    build:
      context: ../backend/akshare-service
      dockerfile: Dockerfile
    container_name: tradingagents-akshare-backend
    ports:
      - "5000:5000"
    environment:
      - APP_NAME=AKShare数据服务
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=5000
      - LOG_LEVEL=INFO
      # 数据库连接配置
      - DB_HOST=localhost
      - DB_PORT=13306
      - DB_NAME=${MYSQL_DATABASE:-trading_analysis}
      - DB_USER=${MYSQL_USER:-trading_user}
      - DB_PASSWORD=${MYSQL_PASSWORD:-trading123}
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # TradingAgents Frontend
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: tradingagents-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://akshare-backend:5000}
      - NEXT_PUBLIC_API_BACKEND_BASE_URL=${NEXT_PUBLIC_API_BACKEND_BASE_URL:-http://akshare-backend:5000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      - BACK_END_URL=${BACK_END_URL:-http://akshare-backend:5000}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
      - AKSHARE_API_URL=${AKSHARE_API_URL:-http://akshare-backend:5000}
      # 数据库连接配置 - Docker内部网络
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=${MYSQL_DATABASE:-trading_analysis}
      - DB_USER=${MYSQL_USER:-trading_user}
      - DB_PASSWORD=${MYSQL_PASSWORD:-trading123}
      # 开发环境特定配置
      - NEXT_TELEMETRY_DISABLED=${NEXT_TELEMETRY_DISABLED:-1}
    env_file:
      - ../.env.development  # 加载开发环境配置文件
    volumes:
      # 挂载源代码以支持开发环境热重载（可选）
      - ..:/app
      - /app/node_modules
      - /app/.next
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - akshare-backend
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL 数据库 - 通过镜像加速器拉取官方镜像
  mysql:
    image: mysql:8.0.28
    container_name: tradingagents-mysql
    ports:
      - "13306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-trading123}
      - MYSQL_DATABASE=${MYSQL_DATABASE:-trading_analysis}
      - MYSQL_USER=${MYSQL_USER:-trading_user}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-trading123}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx 反向代理 (可选) - 通过镜像加速器拉取官方镜像
  nginx:
    image: nginx:alpine
    container_name: tradingagents-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - frontend
    profiles:
      - nginx

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network

volumes:
  mysql_data:
    driver: local
  nginx-ssl:
    driver: local
