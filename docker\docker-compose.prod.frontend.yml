# 生产环境 - 前端服务 Docker Compose 配置文件
# 仅包含前端服务，用于独立部署前端
version: '3.8'

services:
  # TradingAgents Frontend - 使用阿里云镜像
  frontend:
    image: crpi-h9er8c2pccoo3ze4.cn-hangzhou.personal.cr.aliyuncs.com/ez_trading/frontend:latest
    container_name: tradingagents-frontend
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=production
      - DB_HOST=*************
      - DB_PORT=13306
      - DB_USER=root
      - DB_PASSWORD=trading123
      - DB_NAME=trading_analysis
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
      - BACK_END_URL=${BACK_END_URL:-http://localhost:5000}
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network
