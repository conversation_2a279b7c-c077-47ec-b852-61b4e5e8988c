# AKShare 后端服务依赖包
# 这个文件定义了Python后端服务所需的所有依赖包

# Web框架
fastapi==0.104.1          # 现代、快速的Web框架，用于构建API
uvicorn[standard]==0.24.0 # ASGI服务器，用于运行FastAPI应用

# 数据处理
akshare==1.17.26          # 中国金融数据接口库，核心依赖
pandas==2.1.3             # 数据分析和处理库
numpy==1.25.2             # 数值计算库

# HTTP和网络
requests==2.31.0          # HTTP请求库
httpx==0.25.2             # 异步HTTP客户端

# 数据验证和序列化
pydantic==2.5.0           # 数据验证和设置管理

# 数据库驱动
mysql-connector-python==8.0.28 # MySQL数据库连接器

# 日志和监控
python-multipart==0.0.6   # 处理表单数据
python-json-logger==2.0.7 # JSON格式日志

# 开发和测试工具
pytest==7.4.3            # 测试框架
pytest-asyncio==0.21.1   # 异步测试支持
